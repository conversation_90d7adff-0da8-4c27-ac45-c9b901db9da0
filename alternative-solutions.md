# Các cách khác để sửa lỗi "Maximum update depth exceeded"

## Cách 1: Sử dụng useCallback (Đã áp dụng)
```javascript
const fetchAffiliateStats = useCallback(async () => {
  // ... logic fetch data
}, [timeRange]);

const handleTimeRangeChange = (value) => {
  setTimeRange(value);
};

useEffect(() => {
  fetchAffiliateStats();
}, [fetchAffiliateStats]);
```

## Cách 2: Sử dụng debounce
```javascript
const debounceRef = useRef(null);

const handleTimeRangeChange = (value) => {
  setTimeRange(value);
  
  // Clear timeout cũ
  if (debounceRef.current) {
    clearTimeout(debounceRef.current);
  }
  
  // Tạo timeout mới
  debounceRef.current = setTimeout(() => {
    fetchAffiliateStats();
  }, 300); // Đợi 300ms
};

useEffect(() => {
  fetchAffiliateStats();
}, []); // Chỉ chạy lần đầu
```

## Cách 3: Sử dụng flag để kiểm soát
```javascript
const [isInitialized, setIsInitialized] = useState(false);

const handleTimeRangeChange = (value) => {
  setTimeRange(value);
  if (isInitialized) {
    fetchAffiliateStats();
  }
};

useEffect(() => {
  fetchAffiliateStats();
  setIsInitialized(true);
}, []); // Chỉ chạy lần đầu
```

## Cách 4: Tách riêng effect cho timeRange
```javascript
const handleTimeRangeChange = (value) => {
  setTimeRange(value);
};

// Effect cho lần đầu load
useEffect(() => {
  fetchAffiliateStats();
}, []);

// Effect riêng cho timeRange change
useEffect(() => {
  if (timeRange) {
    fetchAffiliateStats();
  }
}, [timeRange]);
```

## Cách 5: Sử dụng useMemo cho data processing
```javascript
const processedData = useMemo(() => {
  // Xử lý data dựa trên timeRange
  return {
    stats,
    revenueData,
    topAffiliates,
    commissionStatusData
  };
}, [timeRange, stats, revenueData, topAffiliates, commissionStatusData]);

const handleTimeRangeChange = (value) => {
  setTimeRange(value);
  fetchAffiliateStats();
};

useEffect(() => {
  fetchAffiliateStats();
}, []); // Chỉ fetch lần đầu
```

## Cách 6: Sử dụng custom hook
```javascript
// Custom hook
const useAffiliateData = (timeRange) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // fetch logic
        setData(result);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [timeRange]);
  
  return { data, loading };
};

// Trong component
const { data, loading } = useAffiliateData(timeRange);
```

## Khuyến nghị:
- **Cách 1 (useCallback)**: Tốt nhất cho performance và clean code
- **Cách 2 (debounce)**: Tốt khi user thay đổi timeRange liên tục
- **Cách 6 (custom hook)**: Tốt nhất cho reusability và separation of concerns
